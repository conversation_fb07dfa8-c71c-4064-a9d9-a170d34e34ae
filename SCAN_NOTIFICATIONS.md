# QR Code Scan Email Notifications

This document describes the automatic email notification system that sends alerts to QR code owners when their codes are scanned.

## Overview

When a QR code is scanned through either the slug-based redirect (`/{slug}`) or ID-based redirect (`/api/qr/{id}`), the system automatically:

1. Stores scan analytics in the database
2. Looks up the QR code owner's email address
3. Sends an email notification with scan details

## How It Works

### 1. QR Code Scan Flow

```
User scans QR code → Redirect endpoint → Store analytics → Send email notification → Redirect user
```

### 2. Email Notification Trigger

The email notification is sent when:
- A QR code is successfully scanned
- Analytics data is successfully stored
- The QR code has a valid `user_id`
- The user has a valid email address in the database

### 3. Notification Content

The email includes:
- QR code name
- Scan timestamp
- Location (city, country)
- Device information (device type, browser, OS)
- Referrer (if available)

## Email Template

### Subject
```
QR Code Scanned: [QR Code Name]
```

### HTML Content
```html
<h1>QR Code Scan Notification</h1>
<p>Your QR code "<strong>[QR Code Name]</strong>" was just scanned!</p>

<h3>Scan Details:</h3>
<ul>
  <li><strong>Time:</strong> [Scan Time]</li>
  <li><strong>Location:</strong> [City], [Country]</li>
  <li><strong>Device:</strong> [Device Type]</li>
  <li><strong>Browser:</strong> [Browser Name]</li>
  <li><strong>OS:</strong> [Operating System]</li>
  <li><strong>Referrer:</strong> [Referrer URL] (if available)</li>
</ul>

<p>View detailed analytics in your QR Analytics dashboard.</p>

<p>Best regards,<br>QR Analytics Team</p>
```

## Configuration

### Environment Variables

The same email configuration used for the general email API:

```bash
ZOHO_MAIL=<EMAIL>
ZOHO_MAIL_PASSWORD=your-zoho-app-password
```

### Database Requirements

The system requires:
1. A `users` table with `id` and `email` columns
2. QR codes with valid `user_id` references
3. The existing `qr_code_scan_analytics` table

## API Endpoints

### QR Code Redirects (with notifications)

- `GET /api/qr/{id}` - Redirect by QR code ID + send notification
- `GET /{slug}` - Redirect by custom slug + send notification

### Test Notification

- `POST /api/test-scan-notification` - Send a test scan notification

**Test Request Body:**
```json
{
  "userEmail": "<EMAIL>",
  "qrCodeName": "My Test QR Code",
  "city": "New York",
  "country": "United States",
  "device": "Mobile",
  "browser": "Safari",
  "os": "iOS",
  "referrer": "https://example.com"
}
```

## Error Handling

### Graceful Degradation

Email notification failures do NOT prevent QR code redirects. If email sending fails:
- The error is logged
- The redirect continues normally
- Users are still redirected to their intended destination

### Common Issues

1. **Missing Email Configuration**
   - Logs: "Email configuration missing, skipping scan notification"
   - Solution: Set `ZOHO_MAIL` and `ZOHO_MAIL_PASSWORD` environment variables

2. **User Not Found**
   - No email sent if `user_id` is null or user doesn't exist
   - This is normal behavior for anonymous QR codes

3. **Invalid Email Address**
   - Email sending will fail but redirect continues
   - Check user email addresses in database

## Performance Considerations

### Asynchronous Processing

Email notifications are sent asynchronously to avoid delaying QR code redirects:

```typescript
// Send notification asynchronously to not delay the redirect
sendScanNotification(userEmail, qrCode, analyticsData, env).catch(error => {
  console.error('Failed to send scan notification:', error);
});
```

### Rate Limiting

Consider implementing rate limiting for high-traffic QR codes to avoid email spam.

## Testing

### Manual Testing

1. **Test Email Configuration:**
   ```bash
   curl -X POST https://your-domain.pages.dev/api/test-scan-notification \
     -H "Content-Type: application/json" \
     -d '{"userEmail": "<EMAIL>"}'
   ```

2. **Test QR Code Scan:**
   - Create a QR code with a valid user_id
   - Scan the QR code or visit the redirect URL
   - Check email inbox for notification

### Monitoring

Monitor email notifications through:
1. Cloudflare Function logs
2. Email delivery logs in Zoho Mail
3. Application analytics

## Customization

### Email Template

To customize the email template, modify the `sendScanNotification` function in `src/utils.ts`:

```typescript
const emailData = {
  to: userEmail,
  subject: `Custom Subject: ${qrCodeName}`,
  html: `<!-- Your custom HTML template -->`,
  text: `Your custom text template`
};
```

### Notification Conditions

To change when notifications are sent, modify the conditions in the redirect endpoints:

```typescript
// Example: Only send notifications for premium users
if (qrCode.user_id && user.isPremium) {
  const userEmail = await getUserEmail(db, qrCode.user_id);
  // ... send notification
}
```

## Security

- Email addresses are fetched securely from the database
- No sensitive scan data (like IP addresses) is included in emails
- Email sending failures are logged but don't expose user data
- SMTP credentials are stored as environment variables

## Troubleshooting

### Debug Steps

1. Check Cloudflare Function logs for email sending errors
2. Verify environment variables are set correctly
3. Test email configuration with the test endpoint
4. Confirm user exists and has valid email address
5. Check Zoho Mail delivery logs

### Common Log Messages

- `"Scan notification email sent successfully to: [email]"` - Success
- `"Email configuration missing, skipping scan notification"` - Missing env vars
- `"Failed to send scan notification email: [error]"` - Email sending failed
