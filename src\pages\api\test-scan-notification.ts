import type { APIRoute } from 'astro';
import { createCorsHeaders, createErrorResponse, createSuccessResponse, sendScanNotification } from '../../utils';

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore - Astro runtime types
    const env = locals.runtime?.env;

    if (!env) {
      return createErrorResponse('Environment not configured', 503);
    }

    const origin = request.headers.get('Origin');
    const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
    const corsHeaders = createCorsHeaders(origin, allowedOrigins);

    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.userEmail) {
      return createErrorResponse('Missing required field: userEmail', 400, corsHeaders);
    }

    // Create test QR code and scan data
    const testQRCode = {
      id: 'test-qr-code-id',
      name: body.qrCodeName || 'Test QR Code',
      user_id: 'test-user-id'
    };

    const testScanData = {
      scan_time: new Date().toISOString(),
      city: body.city || 'San Francisco',
      country: body.country || 'United States',
      device: body.device || 'Mobile',
      browser: body.browser || 'Chrome',
      os: body.os || 'iOS',
      referrer: body.referrer || null
    };

    // Send test scan notification
    await sendScanNotification(body.userEmail, testQRCode, testScanData, env);

    return createSuccessResponse(
      {
        message: 'Test scan notification sent successfully',
        sentTo: body.userEmail,
        qrCodeName: testQRCode.name
      },
      200,
      corsHeaders
    );

  } catch (error) {
    console.error('Test scan notification API error:', error);
    
    // Return appropriate error message
    const errorMessage = error instanceof Error ? error.message : 'Failed to send test notification';
    
    return createErrorResponse(errorMessage, 500);
  }
};

export const OPTIONS: APIRoute = async ({ request, locals }) => {
  // @ts-ignore - Astro runtime types
  const env = locals.runtime?.env;
  const origin = request.headers.get('Origin');
  const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
  const corsHeaders = createCorsHeaders(origin, allowedOrigins);

  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
};
